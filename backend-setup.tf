# Backend Setup Configuration
# This file creates the S3 bucket and DynamoDB table required for Terraform remote state
# Run this BEFORE configuring the S3 backend in main.tf

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
}

# Local values for common configuration
locals {
  common_tags = {
    Terraform   = "true"
    Environment = var.environment
    Project     = var.project
    Purpose     = "terraform-backend"
    ManagedBy   = "terraform"
  }
}

# S3 Module for Terraform State Storage
module "s3_backend" {
  source = "./modules/s3"

  bucket_name                             = var.terraform_state_bucket
  environment                             = var.environment
  project                                 = var.project
  enable_versioning                       = var.enable_s3_versioning
  sse_algorithm                           = var.s3_sse_algorithm
  enable_lifecycle                        = var.enable_s3_lifecycle
  noncurrent_version_expiration_days      = var.s3_noncurrent_version_expiration_days
  abort_incomplete_multipart_upload_days  = var.s3_abort_incomplete_multipart_upload_days

  tags = local.common_tags
}

# DynamoDB Module for State Locking
module "dynamodb_backend" {
  source = "./modules/dynamodb"

  table_name                      = var.terraform_lock_table
  environment                     = var.environment
  project                         = var.project
  billing_mode                    = var.dynamodb_billing_mode
  hash_key                        = var.dynamodb_hash_key
  hash_key_type                   = var.dynamodb_hash_key_type
  enable_point_in_time_recovery   = var.enable_dynamodb_point_in_time_recovery
  enable_encryption               = var.enable_dynamodb_encryption

  tags = local.common_tags
}

# Outputs
output "s3_bucket_name" {
  description = "Name of the S3 bucket for Terraform state"
  value       = module.s3_backend.bucket_name
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket for Terraform state"
  value       = module.s3_backend.bucket_arn
}

output "s3_bucket_id" {
  description = "ID of the S3 bucket for Terraform state"
  value       = module.s3_backend.bucket_id
}

output "s3_bucket_region" {
  description = "Region of the S3 bucket for Terraform state"
  value       = module.s3_backend.bucket_region
}

output "dynamodb_table_name" {
  description = "Name of the DynamoDB table for state locking"
  value       = module.dynamodb_backend.table_name
}

output "dynamodb_table_arn" {
  description = "ARN of the DynamoDB table for state locking"
  value       = module.dynamodb_backend.table_arn
}

output "dynamodb_table_id" {
  description = "ID of the DynamoDB table for state locking"
  value       = module.dynamodb_backend.table_id
}
