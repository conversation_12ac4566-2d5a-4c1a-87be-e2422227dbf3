#!/bin/bash

# Terraform Backend Setup Script
# This script sets up the S3 bucket and DynamoDB table for Terraform remote state
# Now uses modular S3 and DynamoDB modules

set -e

echo "🚀 Setting up Terraform S3 Backend Infrastructure (Modular)..."
echo "============================================================="

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: AWS CLI is not configured or credentials are invalid"
    echo "Please run 'aws configure' to set up your AWS credentials"
    exit 1
fi

echo "✅ AWS credentials verified"

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "❌ Error: Terraform is not installed"
    echo "Please install Terraform from https://www.terraform.io/downloads.html"
    exit 1
fi

echo "✅ Terraform found"

# Check for custom variables file
VARS_FILE=""
if [ -f "backend-setup.tfvars" ]; then
    echo "✅ Found backend-setup.tfvars file"
    VARS_FILE="-var-file=backend-setup.tfvars"
elif [ -f "backend-setup.tfvars.example" ]; then
    echo "⚠️  Found backend-setup.tfvars.example but no backend-setup.tfvars"
    echo "   Consider copying the example file: cp backend-setup.tfvars.example backend-setup.tfvars"
fi

# Step 1: Initialize Terraform for backend setup
echo ""
echo "📦 Initializing Terraform for backend setup..."
terraform init -input=false

# Step 2: Plan the backend infrastructure
echo ""
echo "📋 Planning backend infrastructure..."
if [ -n "$VARS_FILE" ]; then
    terraform plan $VARS_FILE -out=backend-setup.tfplan
else
    terraform plan -out=backend-setup.tfplan
fi

# Step 3: Apply the backend infrastructure
echo ""
echo "🏗️  Creating S3 bucket and DynamoDB table using modules..."
read -p "Do you want to proceed with creating the backend infrastructure? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    terraform apply backend-setup.tfplan
    echo ""
    echo "✅ Backend infrastructure created successfully!"
else
    echo "❌ Backend setup cancelled"
    rm -f backend-setup.tfplan
    exit 1
fi

# Step 4: Clean up plan file
rm -f backend-setup.tfplan

# Step 5: Instructions for next steps
echo ""
echo "🎉 Backend Setup Complete!"
echo "========================="
echo ""
echo "✅ Modular backend infrastructure created:"
echo "   📦 S3 Module: Terraform state storage bucket"
echo "   🔒 DynamoDB Module: State locking table"
echo ""
echo "Next steps:"
echo "1. The S3 bucket has been created using the S3 module"
echo "2. The DynamoDB table has been created using the DynamoDB module"
echo "3. You can now use the S3 backend configuration in your main.tf"
echo ""
echo "To migrate your existing state to S3 (if you have local state):"
echo "  terraform init -migrate-state"
echo ""
echo "To start fresh with the S3 backend:"
echo "  rm -f terraform.tfstate* .terraform.lock.hcl"
echo "  rm -rf .terraform/"
echo "  terraform init"
echo ""
echo "📁 Module Structure:"
echo "   modules/s3/       - S3 bucket configuration"
echo "   modules/dynamodb/ - DynamoDB table configuration"
echo ""
echo "⚠️  Important: Keep the backend-setup.tf and backend-variables.tf files"
echo "   for managing the backend infrastructure modules"
