#!/bin/bash

# Terraform Backend Setup Script
# This script sets up the S3 bucket and DynamoDB table for Terraform remote state

set -e

echo "🚀 Setting up Terraform S3 Backend Infrastructure..."
echo "=================================================="

# Check if AWS CLI is configured
if ! aws sts get-caller-identity &> /dev/null; then
    echo "❌ Error: AWS CLI is not configured or credentials are invalid"
    echo "Please run 'aws configure' to set up your AWS credentials"
    exit 1
fi

echo "✅ AWS credentials verified"

# Check if Terraform is installed
if ! command -v terraform &> /dev/null; then
    echo "❌ Error: Terraform is not installed"
    echo "Please install Terraform from https://www.terraform.io/downloads.html"
    exit 1
fi

echo "✅ Terraform found"

# Step 1: Initialize Terraform for backend setup
echo ""
echo "📦 Initializing Terraform for backend setup..."
terraform init -input=false

# Step 2: Plan the backend infrastructure
echo ""
echo "📋 Planning backend infrastructure..."
terraform plan -out=backend-setup.tfplan

# Step 3: Apply the backend infrastructure
echo ""
echo "🏗️  Creating S3 bucket and DynamoDB table..."
read -p "Do you want to proceed with creating the backend infrastructure? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    terraform apply backend-setup.tfplan
    echo ""
    echo "✅ Backend infrastructure created successfully!"
else
    echo "❌ Backend setup cancelled"
    rm -f backend-setup.tfplan
    exit 1
fi

# Step 4: Clean up plan file
rm -f backend-setup.tfplan

# Step 5: Instructions for next steps
echo ""
echo "🎉 Backend Setup Complete!"
echo "========================="
echo ""
echo "Next steps:"
echo "1. The S3 bucket 'entanglement-terraform-state-bucket' has been created"
echo "2. The DynamoDB table 'terraform-state-lock' has been created"
echo "3. You can now use the S3 backend configuration in your main.tf"
echo ""
echo "To migrate your existing state to S3 (if you have local state):"
echo "  terraform init -migrate-state"
echo ""
echo "To start fresh with the S3 backend:"
echo "  rm -f terraform.tfstate* .terraform.lock.hcl"
echo "  rm -rf .terraform/"
echo "  terraform init"
echo ""
echo "⚠️  Important: Keep the backend-setup.tf file for managing the backend infrastructure"
echo "   You can move it to a separate directory if desired"
