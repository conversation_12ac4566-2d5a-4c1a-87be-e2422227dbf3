# Terraform S3 Backend Setup

This document explains how to set up and configure Terraform remote state storage using AWS S3 and DynamoDB for state locking.

## Overview

The Terraform configuration has been set up to use an S3 backend for remote state storage with the following components:

- **S3 Bucket**: `entanglement-terraform-state-bucket` - Stores the Terraform state files
- **DynamoDB Table**: `terraform-state-lock` - Provides state locking to prevent concurrent modifications
- **State File Path**: `infrastructure/terraform.tfstate` - The key where the state file is stored in S3

## Prerequisites

1. **AWS CLI configured** with appropriate credentials
2. **Terraform installed** (version >= 1.0)
3. **AWS permissions** to create S3 buckets and DynamoDB tables

Required AWS permissions:
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:CreateBucket",
                "s3:GetBucketVersioning",
                "s3:PutBucketVersioning",
                "s3:PutBucketEncryption",
                "s3:PutBucketPublicAccessBlock",
                "s3:PutLifecycleConfiguration",
                "dynamodb:CreateTable",
                "dynamodb:DescribeTable",
                "dynamodb:GetItem",
                "dynamodb:PutItem",
                "dynamodb:DeleteItem"
            ],
            "Resource": "*"
        }
    ]
}
```

## Setup Process

### Option 1: Automated Setup (Recommended)

Run the provided setup script:

```bash
./setup-backend.sh
```

This script will:
1. Verify AWS credentials and Terraform installation
2. Initialize Terraform
3. Create the S3 bucket with proper configuration
4. Create the DynamoDB table for state locking
5. Provide next steps for migration

### Option 2: Manual Setup

1. **Create backend infrastructure:**
   ```bash
   terraform init
   terraform plan -out=backend-setup.tfplan
   terraform apply backend-setup.tfplan
   ```

2. **Migrate existing state (if you have local state):**
   ```bash
   terraform init -migrate-state
   ```

3. **Start fresh with S3 backend:**
   ```bash
   rm -f terraform.tfstate* .terraform.lock.hcl
   rm -rf .terraform/
   terraform init
   ```

## Backend Configuration Details

The S3 backend is configured in `main.tf` with the following settings:

```hcl
terraform {
  backend "s3" {
    bucket         = "entanglement-terraform-state-bucket"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-state-lock"
  }
}
```

## S3 Bucket Features

The S3 bucket is configured with:

- ✅ **Versioning enabled** - Keeps history of state file changes
- ✅ **Server-side encryption** - AES256 encryption at rest
- ✅ **Public access blocked** - Prevents accidental public exposure
- ✅ **Lifecycle policy** - Automatically removes old versions after 90 days
- ✅ **Proper tagging** - For resource management and cost tracking

## DynamoDB Table Features

The DynamoDB table is configured with:

- ✅ **Pay-per-request billing** - Cost-effective for infrequent access
- ✅ **Hash key: LockID** - Required for Terraform state locking
- ✅ **Proper tagging** - For resource management and cost tracking

## Security Considerations

1. **Bucket Access**: The S3 bucket blocks all public access
2. **Encryption**: State files are encrypted at rest using AES256
3. **Versioning**: Enabled to track changes and allow rollbacks
4. **State Locking**: DynamoDB prevents concurrent state modifications

## Troubleshooting

### Common Issues

1. **Permission Denied**
   - Ensure your AWS credentials have the required permissions
   - Check that the bucket name is globally unique

2. **Bucket Already Exists**
   - S3 bucket names must be globally unique
   - Modify the bucket name in both `main.tf` and `backend-setup.tf`

3. **State Migration Issues**
   - Backup your local state file before migration
   - Use `terraform init -migrate-state` to move existing state

### Useful Commands

```bash
# Check current state location
terraform show

# List state resources
terraform state list

# Force unlock state (use with caution)
terraform force-unlock <LOCK_ID>

# Refresh state from remote
terraform refresh
```

## Cost Considerations

- **S3 Storage**: Minimal cost for state files (typically < $1/month)
- **DynamoDB**: Pay-per-request pricing (typically < $1/month for small teams)
- **Data Transfer**: Minimal for state operations

## Backup and Recovery

- **Automatic Versioning**: S3 versioning keeps historical state files
- **Manual Backup**: Download state files from S3 console if needed
- **Recovery**: Restore from S3 version history if corruption occurs

## Team Collaboration

With the S3 backend configured:

1. **All team members** can access the same state
2. **State locking** prevents conflicts during concurrent operations
3. **Version history** allows tracking of infrastructure changes
4. **Centralized state** eliminates local state file management

## Next Steps

After backend setup:

1. All team members should run `terraform init` to configure their local Terraform
2. Remove any local state files from version control
3. Ensure all team members have appropriate AWS permissions
4. Consider setting up state file monitoring and alerting
