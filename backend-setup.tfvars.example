# Backend Setup Variables Example
# Copy this file to backend-setup.tfvars and customize the values
# Use: terraform apply -var-file="backend-setup.tfvars"

# General Configuration
aws_region  = "us-east-1"
environment = "dev"
project     = "entanglement"

# S3 Configuration
terraform_state_bucket                      = "entanglement-terraform-state-bucket"
enable_s3_versioning                        = true
s3_sse_algorithm                            = "AES256"  # or "aws:kms"
enable_s3_lifecycle                         = true
s3_noncurrent_version_expiration_days       = 90
s3_abort_incomplete_multipart_upload_days   = 7

# DynamoDB Configuration
terraform_lock_table                        = "terraform-state-lock"
dynamodb_billing_mode                       = "PAY_PER_REQUEST"  # or "PROVISIONED"
dynamodb_hash_key                           = "LockID"
dynamodb_hash_key_type                      = "S"
enable_dynamodb_point_in_time_recovery      = false  # Set to true for production
enable_dynamodb_encryption                  = true
