{"version": 4, "terraform_version": "1.12.1", "serial": 6, "lineage": "cae82e2e-6542-5579-fb6c-35f154f4c00d", "outputs": {"s3_bucket_arn": {"value": "arn:aws:s3:::entanglement-dev-s3-bucket", "type": "string"}, "s3_bucket_domain_name": {"value": "entanglement-dev-s3-bucket.s3.amazonaws.com", "type": "string"}, "s3_bucket_id": {"value": "entanglement-dev-s3-bucket", "type": "string"}, "s3_bucket_name": {"value": "entanglement-dev-s3-bucket", "type": "string"}, "s3_bucket_region": {"value": "us-east-1", "type": "string"}, "s3_bucket_regional_domain_name": {"value": "entanglement-dev-s3-bucket.s3.us-east-1.amazonaws.com", "type": "string"}, "s3_encryption_algorithm": {"value": "AES256", "type": "string"}, "s3_versioning_enabled": {"value": true, "type": "bool"}}, "resources": [{"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::entanglement-dev-s3-bucket", "bucket": "entanglement-dev-s3-bucket", "bucket_domain_name": "entanglement-dev-s3-bucket.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "entanglement-dev-s3-bucket.s3.us-east-1.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "a79dd84948c41672e65d402c207b92489cedbeb596686014d1f8c6f83526a332", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3AQBSTGFYJSTF", "id": "entanglement-dev-s3-bucket", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-east-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "dev", "ManagedBy": "terraform", "Name": "entanglement-dev-s3-bucket", "Project": "entanglement", "Purpose": "terraform-state", "Terraform": "true"}, "tags_all": {"Environment": "dev", "ManagedBy": "terraform", "Name": "entanglement-dev-s3-bucket", "Project": "entanglement", "Purpose": "terraform-state", "Terraform": "true"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_lifecycle_configuration", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"bucket": "entanglement-dev-s3-bucket", "expected_bucket_owner": "", "id": "entanglement-dev-s3-bucket", "rule": [{"abort_incomplete_multipart_upload": [{"days_after_initiation": 7}], "expiration": [], "filter": [{"and": [], "object_size_greater_than": null, "object_size_less_than": null, "prefix": "", "tag": []}], "id": "terraform_state_lifecycle", "noncurrent_version_expiration": [{"newer_noncurrent_versions": null, "noncurrent_days": 90}], "noncurrent_version_transition": [], "prefix": "", "status": "Enabled", "transition": []}], "timeouts": null, "transition_default_minimum_object_size": "all_storage_classes_128K"}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["module.s3.aws_s3_bucket.terraform_state"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "entanglement-dev-s3-bucket", "id": "entanglement-dev-s3-bucket", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.terraform_state"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "entanglement-dev-s3-bucket", "expected_bucket_owner": "", "id": "entanglement-dev-s3-bucket", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": null}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.terraform_state"]}]}, {"module": "module.s3", "mode": "managed", "type": "aws_s3_bucket_versioning", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "entanglement-dev-s3-bucket", "expected_bucket_owner": "", "id": "entanglement-dev-s3-bucket", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.s3.aws_s3_bucket.terraform_state"]}]}], "check_results": [{"object_kind": "var", "config_addr": "var.s3_sse_algorithm", "status": "pass", "objects": [{"object_addr": "var.s3_sse_algorithm", "status": "pass"}]}, {"object_kind": "var", "config_addr": "module.s3.var.sse_algorithm", "status": "pass", "objects": [{"object_addr": "module.s3.var.sse_algorithm", "status": "pass"}]}]}