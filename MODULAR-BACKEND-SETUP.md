# Modular Terraform Backend Setup

This document explains the modular approach to setting up Terraform remote state storage using separate S3 and DynamoDB modules.

## Overview

The backend infrastructure has been separated into two reusable modules:

- **S3 Module** (`modules/s3/`) - Manages the S3 bucket for state storage
- **DynamoDB Module** (`modules/dynamodb/`) - Manages the DynamoDB table for state locking

## Architecture

```
backend-setup.tf
├── modules/s3/          # S3 bucket module
│   ├── main.tf          # S3 resources
│   ├── variables.tf     # S3 variables
│   ├── outputs.tf       # S3 outputs
│   └── README.md        # S3 documentation
└── modules/dynamodb/    # DynamoDB table module
    ├── main.tf          # DynamoDB resources
    ├── variables.tf     # DynamoDB variables
    ├── outputs.tf       # DynamoDB outputs
    └── README.md        # DynamoDB documentation
```

## Benefits of Modular Approach

### 🔧 **Modularity**
- **Reusable Components**: S3 and DynamoDB modules can be used independently
- **Separation of Concerns**: Each module handles one specific service
- **Easy Maintenance**: Updates to S3 or DynamoDB logic are isolated

### 📈 **Scalability**
- **Multiple Environments**: Use same modules for dev/staging/prod
- **Multiple Projects**: Reuse modules across different Terraform projects
- **Flexible Configuration**: Each module can be configured independently

### 🛡️ **Security & Best Practices**
- **Consistent Configuration**: Modules enforce security best practices
- **Validation**: Input validation at the module level
- **Documentation**: Each module is self-documented

## Module Features

### S3 Module Features
- ✅ **Versioning**: Configurable bucket versioning
- ✅ **Encryption**: AES256 or KMS encryption options
- ✅ **Public Access Block**: Prevents accidental exposure
- ✅ **Lifecycle Management**: Automatic cleanup of old versions
- ✅ **Validation**: Input validation for security settings

### DynamoDB Module Features
- ✅ **Billing Modes**: PAY_PER_REQUEST or PROVISIONED
- ✅ **Encryption**: Optional server-side encryption
- ✅ **Point-in-Time Recovery**: Optional backup capability
- ✅ **Flexible Schema**: Configurable hash key and type
- ✅ **Cost Optimization**: Pay-per-request by default

## Setup Process

### 1. Configure Variables (Optional)
```bash
cp backend-setup.tfvars.example backend-setup.tfvars
# Edit backend-setup.tfvars with your settings
```

### 2. Run Setup Script
```bash
./setup-backend.sh
```

### 3. Manual Setup (Alternative)
```bash
# Initialize Terraform
terraform init

# Plan with custom variables (optional)
terraform plan -var-file="backend-setup.tfvars" -out=backend-setup.tfplan

# Apply the plan
terraform apply backend-setup.tfplan
```

## Configuration Examples

### Basic Configuration
```hcl
module "s3_backend" {
  source = "./modules/s3"
  
  bucket_name = "my-terraform-state"
  environment = "dev"
  project     = "my-project"
}

module "dynamodb_backend" {
  source = "./modules/dynamodb"
  
  table_name  = "terraform-state-lock"
  environment = "dev"
  project     = "my-project"
}
```

### Advanced Configuration
```hcl
module "s3_backend" {
  source = "./modules/s3"
  
  bucket_name                             = "my-terraform-state"
  environment                             = "prod"
  project                                 = "my-project"
  enable_versioning                       = true
  sse_algorithm                           = "aws:kms"
  noncurrent_version_expiration_days      = 30
  
  tags = {
    CostCenter = "engineering"
    Owner      = "platform-team"
  }
}

module "dynamodb_backend" {
  source = "./modules/dynamodb"
  
  table_name                      = "terraform-state-lock"
  environment                     = "prod"
  project                         = "my-project"
  billing_mode                    = "PROVISIONED"
  read_capacity                   = 10
  write_capacity                  = 10
  enable_point_in_time_recovery   = true
  
  tags = {
    CostCenter = "engineering"
    Owner      = "platform-team"
  }
}
```

## File Structure

```
├── backend-setup.tf              # Main backend configuration using modules
├── backend-variables.tf          # Variables for backend setup
├── backend-setup.tfvars.example  # Example variables file
├── setup-backend.sh              # Automated setup script
└── modules/
    ├── s3/                       # S3 module
    │   ├── main.tf
    │   ├── variables.tf
    │   ├── outputs.tf
    │   └── README.md
    └── dynamodb/                 # DynamoDB module
        ├── main.tf
        ├── variables.tf
        ├── outputs.tf
        └── README.md
```

## Migration from Monolithic Setup

If you have an existing monolithic backend-setup.tf:

1. **Backup existing state**: `cp terraform.tfstate terraform.tfstate.backup`
2. **Run the new modular setup**: `./setup-backend.sh`
3. **Verify resources**: Check that S3 bucket and DynamoDB table exist
4. **Update references**: Any scripts referencing the old resources

## Troubleshooting

### Module Not Found
```bash
# Ensure modules directory exists
ls -la modules/s3/ modules/dynamodb/

# Re-initialize Terraform
terraform init
```

### Variable Validation Errors
- Check `backend-setup.tfvars` for correct values
- Refer to module README files for valid options
- Use `terraform validate` to check syntax

### Permission Issues
- Ensure AWS credentials have S3 and DynamoDB permissions
- Check bucket name is globally unique
- Verify region settings match your AWS configuration

## Next Steps

1. **Use the Backend**: Configure `main.tf` to use the S3 backend
2. **Team Setup**: Share backend configuration with team members
3. **Multiple Environments**: Create separate backend setups for each environment
4. **Monitoring**: Set up CloudWatch alarms for backend resources

## Cost Optimization

- **S3**: Use lifecycle policies to manage old versions
- **DynamoDB**: Use PAY_PER_REQUEST for variable workloads
- **Monitoring**: Set up billing alerts for backend resources
