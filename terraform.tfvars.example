# Example Terraform Variables File
# Copy this file to terraform.tfvars and customize the values
# terraform.tfvars is ignored by git for security

# AWS Configuration
aws_region = "us-east-1"

# VPC Configuration
vpc_name = "entanglement-dev-vpc-v1"
vpc_cidr = "10.0.0.0/16"

# Project Configuration
environment = "dev"
project     = "entanglement"

# Feature Toggles
enable_nat_gateway = true
enable_s3_endpoint = true

# Backend Configuration (for reference - these are hardcoded in main.tf)
terraform_state_bucket = "entanglement-terraform-state-bucket"
terraform_state_key    = "infrastructure/terraform.tfstate"
terraform_lock_table   = "terraform-state-lock"

# Common Tags
common_tags = {
  Terraform   = "true"
  Environment = "dev"
  Project     = "entanglement"
  Owner       = "your-team-name"
  CostCenter  = "engineering"
  Repository  = "infra-virtual-chef-ai-terraform-v1"
}
