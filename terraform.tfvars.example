# Example Terraform Variables File
# Copy this file to terraform.tfvars and customize the values
# terraform.tfvars is ignored by git for security

# AWS Configuration
aws_region = "us-east-1"

# Project Configuration
environment = "dev"
project     = "entanglement"

# S3 Configuration
s3_bucket_name                              = "entanglement-dev-s3-bucket"
enable_s3_versioning                        = true
s3_sse_algorithm                            = "AES256"  # or "aws:kms"
enable_s3_lifecycle                         = true
s3_noncurrent_version_expiration_days       = 90
s3_abort_incomplete_multipart_upload_days   = 7

# Common Tags
common_tags = {
  Terraform   = "true"
  Environment = "dev"
  Project     = "entanglement"
  Owner       = "your-team-name"
  CostCenter  = "engineering"
  Repository  = "infra-virtual-chef-ai-terraform-v1"
}
