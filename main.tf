# Main Terraform configuration file
# This file contains the main infrastructure configuration

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"

  vpc_name             = var.vpc_name
  vpc_cidr             = var.vpc_cidr
  environment          = var.environment
  project              = var.project
  enable_nat_gateway   = var.enable_nat_gateway
  enable_s3_endpoint   = var.enable_s3_endpoint

  tags = var.common_tags
}

