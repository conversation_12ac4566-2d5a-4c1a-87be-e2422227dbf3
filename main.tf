# Main Terraform configuration file
# This file contains the main infrastructure configuration

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # S3 Backend Configuration for Remote State Storage
  backend "s3" {
    bucket         = "entanglement-terraform-state-bucket"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-state-lock"
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"

  vpc_name             = var.vpc_name
  vpc_cidr             = var.vpc_cidr
  environment          = var.environment
  project              = var.project
  enable_nat_gateway   = var.enable_nat_gateway
  enable_s3_endpoint   = var.enable_s3_endpoint

  tags = var.common_tags
}

