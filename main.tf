# Main Terraform configuration file
# This file contains the main infrastructure configuration

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # S3 Backend Configuration for Remote State Storage
  backend "s3" {
    bucket         = "entanglement-terraform-state-bucket"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-state-lock"
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region
}

# S3 Module
module "s3" {
  source = "./modules/s3"

  bucket_name                             = var.s3_bucket_name
  environment                             = var.environment
  project                                 = var.project
  enable_versioning                       = var.enable_s3_versioning
  sse_algorithm                           = var.s3_sse_algorithm
  enable_lifecycle                        = var.enable_s3_lifecycle
  noncurrent_version_expiration_days      = var.s3_noncurrent_version_expiration_days
  abort_incomplete_multipart_upload_days  = var.s3_abort_incomplete_multipart_upload_days

  tags = var.common_tags
}

