# Terraform files to ignore
*.tfstate
*.tfstate.*
*.tfvars
*.tfvars.json

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
*.tfplan

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Terraform directories
.terraform/
.terraform.lock.hcl

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# AWS credentials (should never be in repo)
.aws/
aws-credentials.json

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.backup
*.bak

# Temporary files
*.tmp
*.temp
