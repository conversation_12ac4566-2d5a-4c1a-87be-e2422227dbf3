# VPC Module

This Terraform module creates a comprehensive AWS VPC infrastructure with public, private, and RDS subnets across multiple availability zones.

## Architecture

The module creates the following infrastructure:

### VPC Configuration
- **Name**: `entanglement-dev-vpc-v1`
- **IPv4 CIDR**: `10.0.0.0/16`
- **DNS hostnames**: Enabled
- **DNS resolution**: Enabled

### Subnets (9 total across 6 AZs)

#### Public Subnets (2)
- `us-east-1a`: `********/24`
- `us-east-1b`: `********/24`

#### Private Subnets (2)
- `us-east-1a`: `*********/24`
- `us-east-1b`: `*********/24`

#### RDS Subnets (6)
- `us-east-1a`: `*********/24`
- `us-east-1b`: `*********/24`
- `us-east-1c`: `*********/24`
- `us-east-1d`: `*********/24`
- `us-east-1e`: `*********/24`
- `us-east-1f`: `*********/24`

### Networking Components
- **Internet Gateway**: Provides internet access for public subnets
- **NAT Gateway**: Deployed in `us-east-1b` public subnet for private subnet internet access
- **S3 VPC Endpoint**: Gateway endpoint for private S3 access
- **Route Tables**: Separate routing for public, private, and RDS subnets
- **RDS Subnet Group**: For RDS database deployments

## Usage

```hcl
module "vpc" {
  source = "./modules/vpc"

  vpc_name           = "my-vpc"
  vpc_cidr           = "10.0.0.0/16"
  environment        = "dev"
  project            = "my-project"
  enable_nat_gateway = true
  enable_s3_endpoint = true

  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| vpc_name | Name of the VPC | `string` | `"entanglement-dev-vpc-v1"` | no |
| vpc_cidr | CIDR block for VPC | `string` | `"10.0.0.0/16"` | no |
| enable_dns_hostnames | Enable DNS hostnames in the VPC | `bool` | `true` | no |
| enable_dns_support | Enable DNS support in the VPC | `bool` | `true` | no |
| environment | Environment name | `string` | `"dev"` | no |
| project | Project name | `string` | `"entanglement"` | no |
| enable_nat_gateway | Enable NAT Gateway for private subnets | `bool` | `true` | no |
| enable_s3_endpoint | Enable S3 VPC endpoint | `bool` | `true` | no |
| tags | A map of tags to assign to the resource | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_arn | ARN of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| public_subnet_ids | List of IDs of the public subnets |
| private_subnet_ids | List of IDs of the private subnets |
| rds_subnet_ids | List of IDs of the RDS subnets |
| rds_subnet_group_id | ID of the RDS subnet group |
| internet_gateway_id | ID of the Internet Gateway |
| nat_gateway_id | ID of the NAT Gateway |
| public_route_table_id | ID of the public route table |
| private_route_table_id | ID of the private route table |
| rds_route_table_id | ID of the RDS route table |
| s3_vpc_endpoint_id | ID of the S3 VPC Endpoint |

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| aws | ~> 5.0 |

## Resources

- aws_vpc
- aws_subnet (9 total)
- aws_internet_gateway
- aws_nat_gateway
- aws_eip
- aws_route_table (3 total)
- aws_route_table_association (9 total)
- aws_vpc_endpoint
- aws_db_subnet_group
