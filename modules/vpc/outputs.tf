# VPC module output definitions
# Define VPC module output values here

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_main_route_table_id" {
  description = "ID of the main route table associated with this VPC"
  value       = aws_vpc.main.main_route_table_id
}

output "vpc_default_network_acl_id" {
  description = "ID of the default network ACL"
  value       = aws_vpc.main.default_network_acl_id
}

output "vpc_default_security_group_id" {
  description = "ID of the security group created by default on VPC creation"
  value       = aws_vpc.main.default_security_group_id
}

# Internet Gateway Outputs
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

output "internet_gateway_arn" {
  description = "ARN of the Internet Gateway"
  value       = aws_internet_gateway.main.arn
}

# Public Subnet Outputs
output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_arns" {
  description = "List of ARNs of the public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "public_subnet_availability_zones" {
  description = "List of availability zones of the public subnets"
  value       = aws_subnet.public[*].availability_zone
}

# Private Subnet Outputs
output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_arns" {
  description = "List of ARNs of the private subnets"
  value       = aws_subnet.private[*].arn
}

output "private_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "private_subnet_availability_zones" {
  description = "List of availability zones of the private subnets"
  value       = aws_subnet.private[*].availability_zone
}

# RDS Subnet Outputs
output "rds_subnet_ids" {
  description = "List of IDs of the RDS subnets"
  value       = aws_subnet.rds[*].id
}

output "rds_subnet_arns" {
  description = "List of ARNs of the RDS subnets"
  value       = aws_subnet.rds[*].arn
}

output "rds_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the RDS subnets"
  value       = aws_subnet.rds[*].cidr_block
}

output "rds_subnet_availability_zones" {
  description = "List of availability zones of the RDS subnets"
  value       = aws_subnet.rds[*].availability_zone
}

output "rds_subnet_group_id" {
  description = "ID of the RDS subnet group"
  value       = aws_db_subnet_group.main.id
}

output "rds_subnet_group_arn" {
  description = "ARN of the RDS subnet group"
  value       = aws_db_subnet_group.main.arn
}

# NAT Gateway Outputs
output "nat_gateway_id" {
  description = "ID of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].id : null
}

output "nat_gateway_allocation_id" {
  description = "Allocation ID of the Elastic IP address for the gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].allocation_id : null
}

output "nat_gateway_subnet_id" {
  description = "Subnet ID of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].subnet_id : null
}

output "nat_gateway_network_interface_id" {
  description = "ENI ID of the network interface created by the NAT gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].network_interface_id : null
}

output "nat_gateway_private_ip" {
  description = "Private IP address of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].private_ip : null
}

output "nat_gateway_public_ip" {
  description = "Public IP address of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].public_ip : null
}

# Elastic IP Outputs
output "eip_id" {
  description = "ID of the Elastic IP address"
  value       = var.enable_nat_gateway ? aws_eip.nat[0].id : null
}

output "eip_public_ip" {
  description = "Public IP address of the Elastic IP"
  value       = var.enable_nat_gateway ? aws_eip.nat[0].public_ip : null
}

# Route Table Outputs
output "public_route_table_id" {
  description = "ID of the public route table"
  value       = aws_route_table.public.id
}

output "private_route_table_id" {
  description = "ID of the private route table"
  value       = aws_route_table.private.id
}

output "rds_route_table_id" {
  description = "ID of the RDS route table"
  value       = aws_route_table.rds.id
}

# VPC Endpoint Outputs
output "s3_vpc_endpoint_id" {
  description = "ID of the S3 VPC Endpoint"
  value       = var.enable_s3_endpoint ? aws_vpc_endpoint.s3[0].id : null
}

output "s3_vpc_endpoint_prefix_list_id" {
  description = "Prefix list ID of the S3 VPC Endpoint"
  value       = var.enable_s3_endpoint ? aws_vpc_endpoint.s3[0].prefix_list_id : null
}

# Availability Zones Output
output "availability_zones" {
  description = "List of availability zones used"
  value       = var.availability_zones
}
