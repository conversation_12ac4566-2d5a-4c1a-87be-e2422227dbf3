# S3 Module

This Terraform module creates an S3 bucket configured for storing Terraform state files with security best practices.

## Features

- **Versioning**: Enabled by default to track state file changes
- **Encryption**: Server-side encryption with AES256 or AWS KMS
- **Public Access Block**: Prevents accidental public exposure
- **Lifecycle Management**: Automatic cleanup of old versions
- **Proper Tagging**: Consistent resource tagging

## Usage

```hcl
module "s3_backend" {
  source = "./modules/s3"

  bucket_name       = "my-terraform-state-bucket"
  environment       = "dev"
  project           = "my-project"
  enable_versioning = true
  sse_algorithm     = "AES256"

  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| bucket_name | Name of the S3 bucket | `string` | n/a | yes |
| environment | Environment name | `string` | `"dev"` | no |
| project | Project name | `string` | `"entanglement"` | no |
| enable_versioning | Enable versioning for the S3 bucket | `bool` | `true` | no |
| sse_algorithm | Server-side encryption algorithm | `string` | `"AES256"` | no |
| block_public_acls | Block public ACLs | `bool` | `true` | no |
| block_public_policy | Block public bucket policies | `bool` | `true` | no |
| ignore_public_acls | Ignore public ACLs | `bool` | `true` | no |
| restrict_public_buckets | Restrict public bucket policies | `bool` | `true` | no |
| enable_lifecycle | Enable lifecycle configuration | `bool` | `true` | no |
| lifecycle_rule_id | ID for the lifecycle rule | `string` | `"terraform_state_lifecycle"` | no |
| noncurrent_version_expiration_days | Days to expire noncurrent versions | `number` | `90` | no |
| abort_incomplete_multipart_upload_days | Days to abort incomplete uploads | `number` | `7` | no |
| tags | Map of tags to assign to resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| bucket_id | ID of the S3 bucket |
| bucket_name | Name of the S3 bucket |
| bucket_arn | ARN of the S3 bucket |
| bucket_domain_name | Domain name of the S3 bucket |
| bucket_regional_domain_name | Regional domain name of the S3 bucket |
| bucket_region | AWS region of the S3 bucket |
| versioning_enabled | Whether versioning is enabled |
| encryption_algorithm | Server-side encryption algorithm used |

## Security Features

- **Public Access Block**: All public access is blocked by default
- **Server-side Encryption**: AES256 encryption at rest
- **Versioning**: Enabled to track changes and allow rollbacks
- **Lifecycle Policy**: Automatic cleanup of old versions to manage costs

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Resources

- aws_s3_bucket
- aws_s3_bucket_versioning
- aws_s3_bucket_server_side_encryption_configuration
- aws_s3_bucket_public_access_block
- aws_s3_bucket_lifecycle_configuration
