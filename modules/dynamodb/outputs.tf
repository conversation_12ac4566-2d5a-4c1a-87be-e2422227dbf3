# DynamoDB module output definitions
# Define DynamoDB module output values here

output "table_id" {
  description = "ID of the DynamoDB table"
  value       = aws_dynamodb_table.terraform_state_lock.id
}

output "table_name" {
  description = "Name of the DynamoDB table"
  value       = aws_dynamodb_table.terraform_state_lock.name
}

output "table_arn" {
  description = "ARN of the DynamoDB table"
  value       = aws_dynamodb_table.terraform_state_lock.arn
}

output "table_stream_arn" {
  description = "ARN of the DynamoDB table stream"
  value       = aws_dynamodb_table.terraform_state_lock.stream_arn
}

output "table_stream_label" {
  description = "Timestamp of the DynamoDB table stream"
  value       = aws_dynamodb_table.terraform_state_lock.stream_label
}

output "billing_mode" {
  description = "Billing mode of the DynamoDB table"
  value       = aws_dynamodb_table.terraform_state_lock.billing_mode
}

output "hash_key" {
  description = "Hash key of the DynamoDB table"
  value       = aws_dynamodb_table.terraform_state_lock.hash_key
}

output "point_in_time_recovery_enabled" {
  description = "Whether point-in-time recovery is enabled"
  value       = var.enable_point_in_time_recovery
}

output "encryption_enabled" {
  description = "Whether server-side encryption is enabled"
  value       = var.enable_encryption
}
