# DynamoDB Module

This Terraform module creates a DynamoDB table configured for Terraform state locking with security and cost optimization features.

## Features

- **State Locking**: Prevents concurrent Terraform operations
- **Pay-per-Request Billing**: Cost-effective for infrequent access
- **Server-side Encryption**: Optional encryption at rest
- **Point-in-Time Recovery**: Optional backup and recovery
- **Proper Tagging**: Consistent resource tagging

## Usage

```hcl
module "dynamodb_backend" {
  source = "./modules/dynamodb"

  table_name    = "terraform-state-lock"
  environment   = "dev"
  project       = "my-project"
  billing_mode  = "PAY_PER_REQUEST"
  hash_key      = "LockID"

  tags = {
    Environment = "dev"
    Project     = "my-project"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| table_name | Name of the DynamoDB table | `string` | n/a | yes |
| environment | Environment name | `string` | `"dev"` | no |
| project | Project name | `string` | `"entanglement"` | no |
| billing_mode | Billing mode for the table | `string` | `"PAY_PER_REQUEST"` | no |
| hash_key | Hash key for the table | `string` | `"LockID"` | no |
| hash_key_type | Type of the hash key attribute | `string` | `"S"` | no |
| read_capacity | Read capacity units (PROVISIONED mode only) | `number` | `5` | no |
| write_capacity | Write capacity units (PROVISIONED mode only) | `number` | `5` | no |
| enable_point_in_time_recovery | Enable point-in-time recovery | `bool` | `false` | no |
| enable_encryption | Enable server-side encryption | `bool` | `true` | no |
| kms_key_id | KMS key ID for encryption | `string` | `null` | no |
| tags | Map of tags to assign to resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| table_id | ID of the DynamoDB table |
| table_name | Name of the DynamoDB table |
| table_arn | ARN of the DynamoDB table |
| table_stream_arn | ARN of the DynamoDB table stream |
| table_stream_label | Timestamp of the DynamoDB table stream |
| billing_mode | Billing mode of the DynamoDB table |
| hash_key | Hash key of the DynamoDB table |
| point_in_time_recovery_enabled | Whether point-in-time recovery is enabled |
| encryption_enabled | Whether server-side encryption is enabled |

## Billing Modes

### PAY_PER_REQUEST (Default)
- **Best for**: Variable or unpredictable workloads
- **Cost**: Pay only for what you use
- **Scaling**: Automatic scaling up to 40,000 read/write requests per second

### PROVISIONED
- **Best for**: Predictable workloads
- **Cost**: Fixed cost based on provisioned capacity
- **Scaling**: Manual or auto-scaling configuration required

## Security Features

- **Server-side Encryption**: Enabled by default with AWS managed keys
- **Custom KMS Keys**: Optional customer-managed encryption keys
- **Point-in-Time Recovery**: Optional backup and recovery capability
- **IAM Integration**: Works with AWS IAM for access control

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Resources

- aws_dynamodb_table

## State Locking

This table is specifically designed for Terraform state locking with:
- **Hash Key**: `LockID` (String) - Required for Terraform state locking
- **No Range Key**: Not needed for state locking
- **Simple Schema**: Minimal configuration for cost efficiency
