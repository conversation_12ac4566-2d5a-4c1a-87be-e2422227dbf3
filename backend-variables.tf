# Backend Setup Variables
# Variables for configuring the S3 and DynamoDB backend infrastructure

# General Configuration
variable "aws_region" {
  description = "AWS region for backend resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "entanglement"
}

# S3 Configuration
variable "terraform_state_bucket" {
  description = "Name of the S3 bucket for Terraform state"
  type        = string
  default     = "entanglement-terraform-state-bucket"
}

variable "enable_s3_versioning" {
  description = "Enable versioning for the S3 bucket"
  type        = bool
  default     = true
}

variable "s3_sse_algorithm" {
  description = "Server-side encryption algorithm for S3"
  type        = string
  default     = "AES256"
  validation {
    condition     = contains(["AES256", "aws:kms"], var.s3_sse_algorithm)
    error_message = "SSE algorithm must be either AES256 or aws:kms."
  }
}

variable "enable_s3_lifecycle" {
  description = "Enable lifecycle configuration for the S3 bucket"
  type        = bool
  default     = true
}

variable "s3_noncurrent_version_expiration_days" {
  description = "Number of days after which to expire noncurrent versions"
  type        = number
  default     = 90
}

variable "s3_abort_incomplete_multipart_upload_days" {
  description = "Number of days after which to abort incomplete multipart uploads"
  type        = number
  default     = 7
}

# DynamoDB Configuration
variable "terraform_lock_table" {
  description = "Name of the DynamoDB table for state locking"
  type        = string
  default     = "terraform-state-lock"
}

variable "dynamodb_billing_mode" {
  description = "Controls how you are charged for read and write throughput"
  type        = string
  default     = "PAY_PER_REQUEST"
  validation {
    condition     = contains(["PAY_PER_REQUEST", "PROVISIONED"], var.dynamodb_billing_mode)
    error_message = "Billing mode must be either PAY_PER_REQUEST or PROVISIONED."
  }
}

variable "dynamodb_hash_key" {
  description = "Hash key for the DynamoDB table"
  type        = string
  default     = "LockID"
}

variable "dynamodb_hash_key_type" {
  description = "Type of the hash key attribute"
  type        = string
  default     = "S"
  validation {
    condition     = contains(["S", "N", "B"], var.dynamodb_hash_key_type)
    error_message = "Hash key type must be S (string), N (number), or B (binary)."
  }
}

variable "enable_dynamodb_point_in_time_recovery" {
  description = "Enable point-in-time recovery for the DynamoDB table"
  type        = bool
  default     = false
}

variable "enable_dynamodb_encryption" {
  description = "Enable server-side encryption for the DynamoDB table"
  type        = bool
  default     = true
}
