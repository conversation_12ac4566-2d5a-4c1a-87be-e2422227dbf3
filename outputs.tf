# Output definitions for the root module
# Define output values here

# S3 Outputs
output "s3_bucket_id" {
  description = "ID of the S3 bucket"
  value       = module.s3.bucket_id
}

output "s3_bucket_name" {
  description = "Name of the S3 bucket"
  value       = module.s3.bucket_name
}

output "s3_bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = module.s3.bucket_arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = module.s3.bucket_domain_name
}

output "s3_bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = module.s3.bucket_regional_domain_name
}

output "s3_bucket_region" {
  description = "AWS region of the S3 bucket"
  value       = module.s3.bucket_region
}

output "s3_versioning_enabled" {
  description = "Whether versioning is enabled for the S3 bucket"
  value       = module.s3.versioning_enabled
}

output "s3_encryption_algorithm" {
  description = "Server-side encryption algorithm used for S3"
  value       = module.s3.encryption_algorithm
}
